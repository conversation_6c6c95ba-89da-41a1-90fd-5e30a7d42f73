@page "/ChildCaseList"
@layout YCRSLayout
@inject YCRSDbContext _context
@using Intra2025.Components.Base
@using Intra2025.Components.Layout
@using Intra2025.Data
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Intra2025.Models
@using Microsoft.EntityFrameworkCore
@using static Intra2025.Components.Base.JsonDataLoader
@using static Intra2025.Components.Base.BaseFunc
@using Microsoft.AspNetCore.WebUtilities
@using System.Web
@using Intra2025.Components.Shared
@using Intra2025.Services
@inherits BasePageComponent
@inject ILogger<ChildCaseList> new Logger
@inject DataMaskingService DataMaskingService
@inject BusinessLogicValidationService ValidationService

<title>守護寶貝即時通(資料清單)</title>

<style>
    table thead tr th {
        text-align: center;
        vertical-align: middle;
        background-color: #d5e1df !important;
        color: #142d4c
    }

    .status-confirmed {
        color: gray;
        font-size: 12px;
    }

    .status-unconfirmed {
        color: red;
        font-size: 12px;
    }
</style>

<!-- 守護寶貝即時通 Title -->
<div style="display: flex;justify-content: center;align-items: center;">
    <div class="loader2"></div>
</div>

<!-- 使用者資訊和導航現在由 YCRSLayout 統一處理 -->

<!-- 日期選擇與搜尋區塊 -->
<div class="d-flex flex-wrap align-items-center border p-3 rounded bg-light" style="width:1150px; margin-left: 2rem;margin-bottom:0.5rem">
    <!-- 功能按鈕區塊 -->
    <div class="d-flex align-items-center">
        <button class="btn btn-primary me-3" style="background-color:cornflowerblue; border-radius: 10px; width:60px; height:30px;" @onclick="CreateNewRecord">
            新增
        </button>
    </div>
    <!-- 日期選擇 -->
    <div class="me-3" style="width:150px">
        <label for="startDate" class="form-label mb-0 fw-bold">案件起始日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt1" id="startDate" class="form-control" style="width: 140px;" />
    </div>

    <div class="me-3" style="width:150px">
        <label for="endDate" class="form-label mb-0 fw-bold">案件結束日期：</label>
        <InputDate TValue="DateTime?" @bind-Value="dt2" id="endDate" class="form-control" style="width: 140px;" />
    </div>

  
    @if (_userState.IsAdmin)
    {
        <!-- 單位選擇（多選下拉） -->
        <div class="me-3" style="width:210px">
            <label for="unitSelect" class="form-label mb-0 fw-bold">選擇戶所：</label><span style="font-size:11px;color:cornflowerblue">多選請用「ctrl」鍵 + 單位</span>
            <InputSelectMultiple @bind-Value="SelectedUnits" AllValues="@Belongs.Select(u => u.Id).ToList()" class="form-select" multiple style="width: 180px; height: 140px">
                @* 全選選項已由元件自動產生 *@
                @foreach (var unit in Belongs)
                {
                    <option value="@unit.Id">@unit.Name</option>
                }
            </InputSelectMultiple>
        </div>
    }

    <!-- 搜尋輸入框與按鈕 -->
    <div class="d-flex align-items-center">
        <input type="text" @bind="searchTerm" placeholder="搜尋「系統代碼」、「子女姓名」或「身分證字號」"
               class="form-control me-2" style="width: 310px;font-size: 12px;" maxlength="50" />
        <button class="btn btn-outline-primary me-2" @onclick="Search">
            搜尋
        </button>
        <button class="btn btn-outline-secondary" @onclick="Clear">
            清除搜尋
        </button>
    </div>
</div>


<table class="table table-striped">
    <thead style="line-height: 22px;">

        <tr>
            <th style="text-align: center; vertical-align: middle; width: 20px;">序號</th>
            <th style="text-align: center; vertical-align: middle; width: 50px;">子女姓名<br />及身份證</th>
            <th style="text-align: center; vertical-align: middle; width: 50px;">子女出生<br />年月日</th>
            <th style="text-align: center; vertical-align: middle; width: 120px;">父母姓名及身分證</th>
            @* <th style="text-align: center; vertical-align: middle; width: 300px;">案家地址</th> *@
            <th style="text-align: center; vertical-align: middle; width: 120px;">聯絡人姓名/關係</th>
            <th style="text-align: center; vertical-align: middle; width: 180px;">個案類型</th>
            <th style="text-align: center; vertical-align: middle; width: 80px;">案件所屬月份</th>
            <th style="text-align: center; vertical-align: middle; width: 100px;">案件所屬戶所</th>
            <th style="text-align: center; vertical-align: middle; width: 110px;">
                確認狀態
                <div style="margin-top: 5px; margin-bottom: 5px; border-bottom: 2px solid #ccc;"></div> <!-- 添加分隔線 -->
                維護
            </th>
        </tr>
    </thead>
    <tbody>
        @if (FilteredRecords.Any())
        {
            int i = 1;
            @foreach (var record in pagedRecords)
            {
                var radioGroupName = $"group-{record.Id}"; // 定義變數儲存 Name
                <tr>
                    <td style="text-align: center">@i</td>
                    <td>
                        <div>@record.ChildName</div>
                        <div>@(DataMaskingService.ShouldMaskData(_userState.IsAdmin, record.CaseBelongId == _userState.UnitCode)
                            ? DataMaskingService.MaskIdNumber(record.ChildIdNumber)
                            : record.ChildIdNumber)</div>
                    </td>
                    <td>
                        @{
                            DateTime birthDateTime = @record.BirthDate.HasValue
                            ? @record.BirthDate.Value.Date
                            : DateTime.MinValue;

                            string displayDate = (birthDateTime == DateTime.MinValue)
                            ? "無資料"
                            : DateConverter.ToTaiwanCalendar(birthDateTime);
                            @displayDate
                        }
                    </td>
                    <td>
                        @if (!string.IsNullOrWhiteSpace(record.Parent1Name))
                        {
                            <div>
                                父:@record.Parent1Name
                                @if (!string.IsNullOrWhiteSpace(record.Parent1Id))
                                {
                                    <span>(@(DataMaskingService.ShouldMaskData(_userState.IsAdmin, record.CaseBelongId == _userState.UnitCode)
                                        ? DataMaskingService.MaskIdNumber(record.Parent1Id)
                                        : record.Parent1Id))</span>
                                }
                            </div>
                        }
                        @if (!string.IsNullOrWhiteSpace(record.Parent2Name))
                        {
                            <div>
                                母:@record.Parent2Name
                                @if (!string.IsNullOrWhiteSpace(record.Parent2Id))
                                {
                                    <span>(@(DataMaskingService.ShouldMaskData(_userState.IsAdmin, record.CaseBelongId == _userState.UnitCode)
                                        ? DataMaskingService.MaskIdNumber(record.Parent2Id)
                                        : record.Parent2Id))</span>
                                }
                            </div>
                        }
                    </td>
                    @*<td>
                        <span>(戶籍)@record.HouseholdAddress </span><br />
                        <span>(居住)@record.CurrentAddress</span>
                    </td> *@
                    <td>@record.Contactor<br />@record.ContactCommunication</td><!--案家電話 -->
                    <td><span>@GetCategoryName(record.AttributesId)</span></td><!--個案類型 -->
                    <td style="text-align: center">
                        @{
                            DateTime belongDateTime = record.BelongDate.HasValue
                            ? record.BelongDate.Value.Date
                            : DateTime.MinValue;

                            displayDate = (belongDateTime == DateTime.MinValue)
                            ? "無資料"
                            : $"{belongDateTime.Year - 1911:D3}-{belongDateTime.Month:D2}"; // 民國年格式
                            @displayDate
                        }
                    </td>
                    <td>
                        <!--案件所屬戶所 -->
                        @GetBelongName(@record.CaseBelongId)
                    </td>
                    <td style="text-align: center">
                        <div class="@GetStatusCssClass(@record.IsChecked)">@GetStatusMessage(@record.IsChecked)</div> <!--回傳回報狀態-->
                        @if (_userState.IsAdmin || record.CaseBelongId == _userState.UnitCode)
                        {
                            <div>
                                <button style="font-size: 8pt;" @onclick="async () => await HandleRadioChange(record)">變更【確認狀態】</button>
                            </div>
                        }
                        <div><hr></div>
                        <div>
                            <button class="btn btn-warning btn-sm"
                                    @onclick='() => EditRecord(record.Id ?? "")'>編輯</button>
                            <button class="btn btn-danger btn-sm"
                                    @onclick='() => ConfirmDelete(record.Id ?? "")'
                                    disabled="@(record.IsChecked == true)">
                                刪除
                            </button>
                        </div>

                    </td>
                </tr>
                i++;
            }
        }
        else
        {
            <tr>
                <td colspan="5" class="text-center">找不到符合的資料</td>
            </tr>
        }
    </tbody>
</table>

<div class="pagination">
    <div class="pagination-controls">
        <button @onclick="PreviousPage" disabled="@(currentPage == 1)">上一頁</button>
        <span>第 @currentPage 頁</span><span>【共 @totalPages 頁】(案件總數量: @totalRecordCount)</span>
        <button @onclick="NextPage" disabled="@(currentPage == totalPages)">下一頁</button>
    </div>
    <button class="new-link-button" @onclick="ExportDataAsync">匯出ODS檔</button>
</div>

<!-- 刪除確認 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(ShowDeleteModal ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>您確定要刪除此筆資料嗎？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" @onclick="DeleteRecord">確認</button>
                <button type="button" class="btn btn-secondary" @onclick="CloseDeleteModal">取消</button>
            </div>
        </div>
    </div>
</div>
<!-- 錯誤訊息 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(ShowErrorModal ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>@ErrorMessage</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" @onclick="CloseErrorModal">關閉</button>
            </div>
        </div>
    </div>
</div>
<script>
    function showToastAndDownload(message, filePath) {
    alert(message);

    if (filePath) {
    const link = document.createElement('a');
    link.href = filePath + "?v=" + new Date().getTime(); // 添加查詢參數避免快取
    console.log("下載路徑：", filePath);

    link.download = filePath.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    } else {
    console.error("檔案路徑無效，無法下載");
    }
    }
</script>

@code {
    private int currentPage = 1;
    private int pageSize = 10;
    private int totalRecordCount = 0;
    private int totalPages => (int)Math.Ceiling((double)FilteredRecords.Count / pageSize);
    private string clientIp = "";
    private string searchTerm = "";
    private bool ShowDeleteModal { get; set; } = false;
    private string? DeleteRecordId { get; set; }
    private List<Category> Categories = new(); // JSON 中的分類資料
    private List<CaseBelong> Belongs = new(); // JSON 中的「12間戶所資料」
    private DateTime? dt1, dt2;

    private List<YCRS_ChildRecord> ChildRecords = new();
    private List<YCRS_ChildRecord> pagedRecords = new List<YCRS_ChildRecord>();
    private List<YCRS_ChildRecord> FilteredRecords = new List<YCRS_ChildRecord>();
    private Dictionary<string, List<YCRS_Files>> YCRSFiles = new Dictionary<string, List<YCRS_Files>>();
    private bool ShowErrorModal = false; // 控制錯誤 Modal 是否顯示
    private string ErrorMessage = string.Empty; // 錯誤訊息內容
    private List<string> _selectedUnits = new();
    private List<string> SelectedUnits
    {
        get => _selectedUnits;
        set
        {
            _selectedUnits = value;
            // 戶所選擇時不立即篩選，等待用戶按「搜尋」時再篩選
        }
    }
    private List<string> AvailableUnits = new List<string>(); // 反序列化 JSON 資料到物件
    private bool hasSearched = false;


    [Inject]
    protected ILoggerFactory _loggerFactory { get; set; } = null!;
    [Inject]
    private IWebHostEnvironment _env { get; set; } = null!;

    protected override async Task OnInitializedAsync()
    {
        // 解析 QueryString
        var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
        var query = QueryHelpers.ParseQuery(uri.Query);
        if (query.TryGetValue("searchTerm", out var qSearchTerm))
            searchTerm = qSearchTerm.ToString();
        if (query.TryGetValue("dt1", out var qDt1) && DateTime.TryParse(qDt1, out var parsedDt1))
            dt1 = parsedDt1;
        if (query.TryGetValue("dt2", out var qDt2) && DateTime.TryParse(qDt2, out var parsedDt2))
            dt2 = parsedDt2;
        if (query.TryGetValue("currentPage", out var qPage) && int.TryParse(qPage, out var parsedPage))
            currentPage = parsedPage;
        //抓取Client IP
        clientIp = ClientIpService.ClientIp;
        //_userState = sso.ValidateAndInitializeUser2(_userState);
        

        try
        {
            // 先查詢所有資料，馬上 ToListAsync()
            var allChildRecords = await _context.YCRS_ChildRecord
                .OrderByDescending(record => record.Id)
                .ToListAsync();

            // 根據權限過濾資料
            if (_userState.IsAdmin)
            {
                ChildRecords = allChildRecords;
            }
            else
            {
                ChildRecords = allChildRecords
                    .Where(record => record.CaseBelongId == _userState.UnitCode)
                    .ToList();
                if (!ChildRecords.Any())
                {
                    ErrorMessage = "目前無任何屬於您單位的資料。";
                    ShowErrorModal = true;
                }
            }

            // 抓取報告檔
            var files = await _context.YCRS_Files.ToListAsync();
            foreach (var file in files)
            {
                if (!YCRSFiles.ContainsKey(file.Id?.ToString() ?? ""))
                {
                    YCRSFiles[file.Id?.ToString() ?? ""] = new List<YCRS_Files>();
                }
                YCRSFiles[file.Id?.ToString() ?? ""].Add(file);
            }

            // 初始化分頁資料
            FilteredRecords = ChildRecords;
            totalRecordCount = ChildRecords.Count; // 計算資料總筆數
            UpdatePaged();

            // 載入 JSON 資料
            var jsonDataLoader = new JsonDataLoader(env);
            Categories = await jsonDataLoader.LoadCategoriesFromJsonAsync("A");
            Belongs = await jsonDataLoader.LoadCaseBelongFromJsonAsync();

            // 預設全選，將所有單位的 Id 加入到 SelectedUnits
            SelectedUnits = Belongs.Select(unit => unit.Id).Where(id => id != null).Cast<string>().ToList();
        }
        catch (Exception ex)
        {
            ErrorMessage = $"資料初始化失敗：{ex.Message}";
            ShowErrorModal = true;
        }

        FilterRecords(); // 初始化完成後進行一次篩選以顯示資料
        await Task.CompletedTask; // 非同步方法需要使用 await，即使沒有非同步邏輯
    }

    private async Task HandleRadioChange(YCRS_ChildRecord record)
    {
        try
        {
            // 業務邏輯驗證
            var validationResult = await ValidationService.ValidateStatusChangeAsync(
                record, _userState.Account, _userState.IsAdmin, _userState.UnitCode);

            if (!validationResult.IsValid)
            {
                ErrorMessage = validationResult.ErrorMessage;
                ShowErrorModal = true;
                return;
            }

            // 記錄原始狀態用於日誌
            var originalStatus = record.IsChecked;
            var newStatus = !record.IsChecked;

            // 變更狀態
            record.IsChecked = newStatus;

            // 將變更寫回資料庫
            _context.YCRS_ChildRecord.Update(record);

            // 記錄詳細的稽核日誌
            var logMessage = $"變更確認狀態：{(originalStatus ? "已確認" : "未確認")} → {(newStatus ? "已確認" : "未確認")}";
            await AccessLogHelper.SaveAccessLogAsync(_context, record.Id ?? "", logMessage, _userState.Account, clientIp ?? "");

            await _context.SaveChangesAsync(); // 非同步保存

            // 重新載入資料以確保一致性
            StateHasChanged();
        }
        catch (Exception ex)
        {
            // 改善錯誤處理，避免洩露敏感資訊
            ErrorMessage = "變更確認狀態時發生錯誤，請稍後再試。";
            ShowErrorModal = true;
            Console.WriteLine($"Error in HandleRadioChange: {ex.Message}");
        }
    }

    private Task CreateNewRecord()
    {
        // 導航到新增記錄的頁面，帶上查詢參數
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={(dt1.HasValue ? dt1.Value.ToString("yyyy-MM-dd") : "")}&dt2={(dt2.HasValue ? dt2.Value.ToString("yyyy-MM-dd") : "")}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ChildCaseNewEdit/{query}", true);
        return Task.CompletedTask;
    }

    private void EditRecord(string id)
    {
        // 編輯時帶上查詢參數
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={(dt1.HasValue ? dt1.Value.ToString("yyyy-MM-dd") : "")}&dt2={(dt2.HasValue ? dt2.Value.ToString("yyyy-MM-dd") : "")}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ChildCaseNewEdit/{id}{query}", true);
    }

    private void ConfirmDelete(string id)
    {
        DeleteRecordId = id;
        ShowDeleteModal = true;
        StateHasChanged();
    }

    private void CloseDeleteModal()
    {
        ShowDeleteModal = false;
    }

    private async Task DeleteRecord()
    {
        try
        {
            // 檢查 DeleteRecordId 是否為空
            if (string.IsNullOrEmpty(DeleteRecordId))
            {
                ErrorMessage = "無效的記錄 ID，無法刪除。";
                ShowErrorModal = true;
                return;
            }

            // 業務邏輯驗證
            var validationResult = await ValidationService.ValidateDeleteAsync<YCRS_ChildRecord>(
                DeleteRecordId, _userState.Account, _userState.IsAdmin, _userState.UnitCode);

            if (!validationResult.IsValid)
            {
                ErrorMessage = validationResult.ErrorMessage;
                ShowErrorModal = true;
                return;
            }

            // 取得記錄進行刪除
            var record = await _context.YCRS_ChildRecord.FindAsync(DeleteRecordId);
            if (record != null)
            {
                // 記錄詳細的稽核日誌
                var logMessage = $"刪除記錄 - 兒童姓名: {record.ChildName}, 案件所屬: {record.CaseBelongId}";
                await AccessLogHelper.SaveAccessLogAsync(_context, DeleteRecordId ?? "UnknownId", logMessage, _userState.Account, clientIp);

                _context.YCRS_ChildRecord.Remove(record);
                await _context.SaveChangesAsync();

                NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ChildCaseList", true);
            }
            else
            {
                ErrorMessage = "記錄不存在，無法刪除。";
                ShowErrorModal = true;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "刪除兒童案件時發生錯誤，記錄 ID: {DeleteRecordId}", DeleteRecordId);
            ErrorMessage = "刪除過程中發生無法預期的錯誤，請聯繫系統管理員。";
            ShowErrorModal = true;
        }
    }

    private string GetStatusMessage(bool isChecked)
    {
        // 返回純文字訊息，樣式由 CSS 類別控制
        return isChecked ? "已確認" : "未確認";
    }

    private string GetStatusCssClass(bool isChecked)
    {
        // 返回對應的 CSS 類別
        return isChecked ? "status-confirmed" : "status-unconfirmed";
    }

    // 根據 AttributesId 查找對應的中文名稱
    private string GetCategoryName(string? attributesId)
    {
        var category = Categories.FirstOrDefault(c => c.Id == attributesId);
        return category?.Name ?? "無此分類";
    }


    // 根據 CaseBelongId 查找對應的中文名稱
    private string GetBelongName(string? belongsId)
    {
        var belongs = Belongs.FirstOrDefault(c => c.Id == belongsId);
        return belongs?.Name ?? "無此分類";
    }

    private void Search()
    {
        currentPage = 1;
        hasSearched = true;
        FilterRecords();
    }

    private void Clear()
    {
        searchTerm = "";
        dt1 = null;
        dt2 = null;
        currentPage = 1;
        SelectedUnits = Belongs.Select(unit => unit.Id).Where(id => id != null).Cast<string>().ToList(); // 清除搜尋時戶所全選
        hasSearched = true;
        FilterRecords(); // 清除搜尋時立即顯示結果
    }

    private void FilterRecords()
    {
        try
        {
            // 根據角色篩選資料
            var recordsToFilter = _userState.IsAdmin
                ? ChildRecords.Where(r => SelectedUnits.Contains(r.CaseBelongId ?? "")).ToList() // Admin 根據「選擇單位」過濾
               : ChildRecords
        .Where(r => r.CaseBelongId == _userState.UnitCode && SelectedUnits.Contains(r.CaseBelongId ?? ""))
        .ToList(); // User 僅能看到與所屬單位匹配且符合「選擇單位」的資料

            if (!recordsToFilter.Any())
            {
                if (hasSearched)
                {
                    ErrorMessage = "目前無任何符合條件的資料可供篩選。";
                    ShowErrorModal = true;
                }
            }
            else
            {
                // 套用搜尋與篩選條件
                FilteredRecords = recordsToFilter
                    .Where(r =>
                        // 搜尋條件
                        (string.IsNullOrWhiteSpace(searchTerm) ||
                            (r.ChildName != null && r.ChildName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                            (r.ChildIdNumber != null && r.ChildIdNumber.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                            (r.Id?.ToString().Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true))
                        &&
                        // 日期篩選條件
                        (!dt1.HasValue && !dt2.HasValue ||
                            (r.BelongDate.HasValue &&
                             (!dt1.HasValue || r.BelongDate >= dt1) &&
                             (!dt2.HasValue || r.BelongDate <= dt2.Value)))

                    )
                    .ToList();

                // 更新總筆數
                totalRecordCount = FilteredRecords.Count;

                if (totalRecordCount == 0 && hasSearched)
                {
                    ErrorMessage = "篩選條件下無符合的資料。";
                    ShowErrorModal = true;
                }
            }

            // 只更新分頁，不重設 currentPage
            UpdatePaged();
        }
        catch (Exception ex)
        {
            ErrorMessage = $"篩選過程中發生錯誤：{ex.Message}";
            ShowErrorModal = true;
        }
    }

    private void UpdatePaged()
    {
        pagedRecords = FilteredRecords
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePaged();
        }
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePaged();
        }
    }

    private void HandleUnitSelectionChange(ChangeEventArgs e)
    {
        if (e.Value is IEnumerable<string> selectedValues)
        {
            SelectedUnits = selectedValues.ToList();
        }
        else
        {
            SelectedUnits.Clear();
        }
    }
    
    public async Task ExportDataAsync()
    {  // 確保有 ILogger 實例
        var logger = _loggerFactory.CreateLogger<ExportODFService<YCRS_ChildRecord>>();
        // 先產生 CSV 檔案
        var exporter = new ExportODFService<YCRS_ChildRecord>(env, logger);        
        //var exporter = new ExportODFService<YCRS_ChildRecord>(env);
        var guid = Guid.NewGuid().ToString();
        var fileBaseName = DateTime.Now.ToString("MMdd-hhmmss") + "_守護寶貝即時通彙整名冊_" + guid;
        var csvFileName = fileBaseName + ".csv";
        var odsFileName = fileBaseName + ".ods";
        string exportFolderPath = Path.Combine(_env.ContentRootPath, "App_Data", "Exports");
        Directory.CreateDirectory(exportFolderPath); // 確保目錄存在
        string csvFilePath = Path.Combine(exportFolderPath, csvFileName);
        string odsFilePath = Path.Combine(exportFolderPath, odsFileName);

        exporter.ExportToChildRecord(FilteredRecords, csvFilePath);
        exporter.ConvertCsvToOds(csvFilePath, odsFilePath);

        // 顯示通知訊息並下載 ODS
        if (File.Exists(odsFilePath))
        {
            await JS.InvokeVoidAsync("showToastAndDownload", "ODS檔已產生成功", "/download/" + odsFileName);
            await Task.Delay(3000); //3s 後刪除檔案
            try { File.Delete(csvFilePath); File.Delete(odsFilePath); } catch { }
        }
        else
        {
            await JS.InvokeVoidAsync("showToastAndDownload", "ODS檔案生成失敗，請重試", "");
        }
    }

    // 關閉錯誤訊息 Modal
    private void CloseErrorModal()
    {
        ShowErrorModal = false;
        ErrorMessage = string.Empty;
    }

    private void OnUnitsChanged(List<string> units)
    {
        SelectedUnits = units;
        // 戶所選擇變更時不立即篩選，等待用戶按「搜尋」
    }
}