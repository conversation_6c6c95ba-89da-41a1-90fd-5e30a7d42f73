using Intra2025.Data;
using Intra2025.Components;
using Intra2025.Services;
using Intra2025.Components.Base;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.HttpOverrides;
using System.Net;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Infrastructure;
using Newtonsoft.Json.Linq;


var builder = WebApplication.CreateBuilder(args);

// 配置 Kestrel 監聽所有介面並使用自定義憑證
builder.WebHost.ConfigureKestrel(options =>
{
    // 監聽所有 IP 地址的 5214 端口，使用自定義憑證
    options.ListenAnyIP(5214, listenOptions =>
    {
        listenOptions.UseHttps(httpsOptions =>
        {
            httpsOptions.ServerCertificateSelector = (connectionContext, name) =>
            {
                // 使用憑證存放區中的憑證
                using var store = new System.Security.Cryptography.X509Certificates.X509Store(
                    System.Security.Cryptography.X509Certificates.StoreName.My,
                    System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser);
                store.Open(System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly);

                var cert = store.Certificates
                    .Find(System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint,
                          "909C0DA1440FAF50730161126AA02BA62A37074F", false)
                    .FirstOrDefault();

                return cert;
            };
        });
    });
});

// 設定 QuestPDF 授權
QuestPDF.Settings.License = LicenseType.Community;

// 添加日誌記錄程序
builder.Logging.ClearProviders();
builder.Logging.AddConsole();


builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(@"C:\keys"))  // 指定資料保護金鑰存放位置
    .SetApplicationName("BBSR_Intra");

builder.Services.AddHttpClient("sso", client =>
{
    client.BaseAddress = new Uri("https://************");
});

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

builder.Services.AddDbContext<AppDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("LocalConn") ?? throw new InvalidOperationException("Connection is not found "));
});
builder.Services.AddDbContext<ReportDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("ReportConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddDbContext<YCRSDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("YCRSConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddDbContext<ComRemitDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("LocalConn") ?? throw new InvalidOperationException("Connection is not found "));
});

builder.Services.AddSingleton<LaunchSettingsService>();

// 註冊 HttpClient
builder.Services.AddHttpClient();
//builder.Services.AddTransient<SSO>();
// 註冊 EmailService
builder.Services.AddScoped<EmailService>();

// 註冊 ComRemit 相關服務
builder.Services.AddScoped<ComRemitPayeeService>();
builder.Services.AddScoped<ComRemitService>();
builder.Services.AddScoped<ComRemitedListService>();
builder.Services.AddScoped<ComRemitQRCodeService>();
builder.Services.AddScoped<ComRemitDatabaseInitializationService>();

// 註冊資料遮罩服務
builder.Services.AddScoped<DataMaskingService>();

// 註冊業務邏輯驗證服務
builder.Services.AddScoped<BusinessLogicValidationService>();

// 註冊業務規則配置服務
builder.Services.AddSingleton<BusinessRulesConfigService>();

// 註冊安全檔案上傳服務
builder.Services.AddScoped<SecureFileUploadService>();
builder.Services.AddSingleton<FileUploadConfigService>();
builder.Services.AddScoped<SecureFileDownloadService>();
builder.Services.AddScoped<FileMigrationService>();

// 添加 Session 支援
builder.Services.AddDistributedMemoryCache(); // 使用記憶體儲存 session
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30); // 設定 Session 過期時間 -> 30分鐘
    options.Cookie.HttpOnly = true; // 僅允許 Http 存取
    options.Cookie.IsEssential = true; // 符合 GDPR 規範
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // 確保 Cookie 只透過 HTTPS 傳輸
    options.Cookie.SameSite = SameSiteMode.Lax; // 設定 SameSite 屬性為 Lax
});

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSpecificOrigins", policy =>
    {
        policy.WithOrigins("https://************", "https://intra.e-land.gov.tw", "https://intra2.e-land.gov.tw")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
    options.KnownProxies.Clear();
    options.KnownNetworks.Clear();
});
// 註冊 ClientIpService 到 DI 容器
builder.Services.AddSingleton<ClientIpService>();  // 使用 Singleton，這樣整個應用程式可以共享同一個服務實例

builder.Services.AddScoped<UserState>(); // 註冊到 DI 容器中*/
builder.Services.AddScoped<SsoService>();
builder.Services.AddScoped<AuditLogService>();
builder.Services.AddScoped<ProjectProgressPresentationService>();

// 註冊安全日誌服務
builder.Services.Configure<Intra2025.Services.SecurityLogOptions>(
    builder.Configuration.GetSection("SecurityLog"));
builder.Services.AddScoped<Intra2025.Services.SecurityEventLogService>();

builder.Services.AddMemoryCache();

builder.Services.AddHttpContextAccessor();

builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();



var app = builder.Build();


// 註冊中介軟體
app.UseMiddleware<ClientIpMiddleware>();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseForwardedHeaders();
app.UseHttpsRedirection();

app.UseSession();

// 取得 ILogger 實例
var logger = app.Services.GetRequiredService<ILogger<Program>>();

// 檢查 PathBase
var configuration = builder.Configuration;
var pathBase = configuration["PathBase"];
if (!string.IsNullOrEmpty(pathBase))
{
    app.UsePathBase(pathBase);
}
app.UseStaticFiles();

// 添加安全 HTTP 標頭
app.Use(async (context, next) =>
{
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Append("Referrer-Policy", "no-referrer-when-downgrade");
    await next();
});
app.UseCors("AllowSpecificOrigins");
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// 安全檔案下載端點
app.MapGet("/api/secure-download", async (HttpContext context, SecureFileDownloadService downloadService, SsoService ssoService, ILogger<Program> logger) =>
{
    var filePath = context.Request.Query["filePath"].ToString();
    logger.LogInformation("Secure download request: {FilePath}", filePath);

    if (string.IsNullOrEmpty(filePath))
    {
        logger.LogWarning("Empty file path in download request");
        context.Response.StatusCode = 400;
        await context.Response.WriteAsync("檔案路徑是必需的。");
        return;
    }

    // URL 解碼檔案路徑
    var decodedFilePath = System.Web.HttpUtility.UrlDecode(filePath);
    logger.LogDebug("Decoded file path: {DecodedFilePath}", decodedFilePath);

    // 直接從 Cookie 驗證使用者身份
    var ssoToken = context.Request.Cookies["PUBLIC_APP_USER_SSO_TOKEN"];
    logger.LogDebug("SSO Token from cookie: {Token}",
        string.IsNullOrEmpty(ssoToken) ? "null" : ssoToken.Substring(0, Math.Min(20, ssoToken.Length)) + "...");

    if (string.IsNullOrEmpty(ssoToken))
    {
        logger.LogWarning("No SSO token found for download request: {FilePath}", decodedFilePath);
        context.Response.StatusCode = 401;
        await context.Response.WriteAsync("需要身份驗證。請重新登入。");
        return;
    }

    // 使用 SsoService 驗證並取得使用者資訊
    var userInfo = await GetUserInfoFromSsoTokenAsync(ssoService, ssoToken, logger);
    if (userInfo == null)
    {
        logger.LogWarning("Invalid SSO token for download request: {FilePath}", decodedFilePath);
        context.Response.StatusCode = 401;
        await context.Response.WriteAsync("身份驗證失敗。請重新登入。");
        return;
    }

    logger.LogInformation("Download request authenticated: {FilePath} by user {UserId}", decodedFilePath, userInfo.Account);

    // 使用安全下載服務
    var result = await downloadService.DownloadFileAsync(
        decodedFilePath,
        userInfo.Account,
        userInfo.IsAdmin,
        userInfo.UnitCode);

    if (!result.IsSuccess)
    {
        var statusCode = result.ErrorMessage.Contains("權限") ? 403 :
                        result.ErrorMessage.Contains("不存在") ? 404 : 400;

        logger.LogWarning("Download failed: {ErrorMessage} for file {FilePath} by user {UserId}",
            result.ErrorMessage, decodedFilePath, userInfo?.Account ?? "Unknown");

        context.Response.StatusCode = statusCode;
        context.Response.ContentType = "text/plain; charset=utf-8";
        await context.Response.WriteAsync(result.ErrorMessage);
        return;
    }

    logger.LogInformation("Download successful: {FileName} for user {UserId}", result.FileName, userInfo.Account);

    // 設定安全的回應標頭
    context.Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{Uri.EscapeDataString(result.FileName)}\"");
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.ContentType = result.ContentType;

    // 傳送檔案內容
    await context.Response.Body.WriteAsync(result.FileBytes);
});

// 保留舊端點以維持向後相容性，但添加警告日誌
app.MapGet("/api/downloadfile", async (HttpContext context, ILogger<Program> logger, ReportDbContext dbContext, UserState userState) =>
{
    logger.LogWarning("使用了不安全的檔案下載端點 /api/downloadfile，建議改用 /api/secure-download");

    var filePathParam = context.Request.Query["filePath"].ToString();

    if (string.IsNullOrEmpty(filePathParam))
    {
        context.Response.StatusCode = 400; // Bad Request
        await context.Response.WriteAsync("File path is required.");
        return;
    }

    // Decode the URL-encoded file path
    var decodedFilePath = System.Net.WebUtility.UrlDecode(filePathParam);

    // Prevent path traversal: Ensure the path is relative and within the allowed directory
    if (decodedFilePath.Contains("..") || decodedFilePath.StartsWith("/") || decodedFilePath.StartsWith(""))
    {
        context.Response.StatusCode = 403; // Forbidden
        await context.Response.WriteAsync("Invalid file path.");
        return;
    }

    // Construct the full physical path
    var webRootPath = app.Environment.WebRootPath;
    var fullPath = Path.Combine(webRootPath, decodedFilePath.TrimStart('/'));

    if (!System.IO.File.Exists(fullPath))
    {
        context.Response.StatusCode = 404; // Not Found
        await context.Response.WriteAsync("File not found.");
        return;
    }

    // --- 權限檢查 (簡化範例，您需要根據實際業務邏輯實作更複雜的權限檢查) ---
    // 這裡假設只要有登入就可以下載，更嚴格的檢查需要從資料庫驗證檔案與使用者的關聯
    if (string.IsNullOrEmpty(userState.Account))
    {
        context.Response.StatusCode = 401; // Unauthorized
        await context.Response.WriteAsync("Authentication required.");
        return;
    }

    // 從資料庫驗證檔案路徑的合法性，防止下載任意檔案
    var fileInDb = await dbContext.ELS_REPORT_FILES
        .FirstOrDefaultAsync(f => f.Filepath == decodedFilePath);

    if (fileInDb == null)
    {
        context.Response.StatusCode = 403; // Forbidden - File not associated with the application or user
        await context.Response.WriteAsync("Access to this file is forbidden.");
        return;
    }

    // 獲取檔案的 MIME 類型
    var contentType = "application/octet-stream"; // 預設 MIME 類型
    var extension = Path.GetExtension(fullPath).ToLowerInvariant();
    switch (extension)
    {
        case ".pdf":
            contentType = "application/pdf";
            break;
        case ".odt":
            contentType = "application/vnd.oasis.opendocument.text";
            break;
        case ".ods":
            contentType = "application/vnd.oasis.opendocument.spreadsheet";
            break;
        case ".odp":
            contentType = "application/vnd.oasis.opendocument.presentation";
            break;
        case ".zip":
            contentType = "application/zip";
            break;
        case ".7z":
            contentType = "application/x-7z-compressed";
            break;
    }

    // 返回檔案
    context.Response.ContentType = contentType;
    context.Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{Uri.EscapeDataString(Path.GetFileName(fullPath))}\"");
    await context.Response.SendFileAsync(fullPath);
});

app.MapGet("/download/{filename}", async (HttpContext context, ILogger<Program> logger, IWebHostEnvironment env, string filename) =>
{
    logger.LogInformation($"Download request received for file: {filename}");

    // 防止路徑遍歷攻擊
    if (filename.Contains("..") || filename.Contains("/") || filename.Contains("\\"))
    {
        context.Response.StatusCode = 400; // Bad Request
        await context.Response.WriteAsync("Invalid filename.");
        return;
    }

    var exportFolderPath = Path.Combine(env.ContentRootPath, "App_Data", "Exports");
    var fullPath = Path.Combine(exportFolderPath, filename);

    if (!System.IO.File.Exists(fullPath))
    {
        context.Response.StatusCode = 404; // Not Found
        await context.Response.WriteAsync("File not found.");
        return;
    }

    // 獲取檔案的 MIME 類型
    var contentType = "application/octet-stream";
    var extension = Path.GetExtension(fullPath).ToLowerInvariant();
    switch (extension)
    {
        case ".ods":
            contentType = "application/vnd.oasis.opendocument.spreadsheet";
            break;
        case ".csv":
            contentType = "text/csv";
            break;
        // 可以根據需要添加其他檔案類型
    }

    // 返回檔案
    context.Response.ContentType = contentType;
    context.Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{Uri.EscapeDataString(Path.GetFileName(fullPath))}\"");
    await context.Response.SendFileAsync(fullPath);

    // 下載完成後刪除檔案
    try
    {
        System.IO.File.Delete(fullPath);
        logger.LogInformation($"Deleted downloaded file: {fullPath}");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, $"Error deleting file {fullPath}");
    }
});

app.Run();

// 輔助方法：從 SSO Token 取得使用者資訊
static Task<UserInfo?> GetUserInfoFromSsoTokenAsync(SsoService ssoService, string ssoToken, ILogger logger)
{
    try
    {
        // 使用 SsoService 的內部方法來驗證 Token 並取得使用者資訊
        JObject? userProfile = null;

        try
        {
            userProfile = ssoService.GetUserBasicProfileFromApi(ssoToken);
        }
        catch (Exception apiEx)
        {
            logger.LogError(apiEx, "Failed to call SSO API");
            return Task.FromResult<UserInfo?>(null);
        }

        if (userProfile == null || userProfile["ERROR_CODE"]?.ToString() != "0")
        {
            logger.LogWarning("SSO token validation failed. Error code: {ErrorCode}",
                userProfile?["ERROR_CODE"]?.ToString() ?? "null");
            return Task.FromResult<UserInfo?>(null);
        }

        var basicProfile = userProfile["APP_USER_BASIC_PROFILE"];
        var employProfile = userProfile["APP_USER_EMPLOY_PROFILE"];

        if (basicProfile == null)
        {
            logger.LogWarning("No APP_USER_BASIC_PROFILE found in SSO response");
            return Task.FromResult<UserInfo?>(null);
        }

        // 檢查是否為管理員
        var adminList = new[] { "stan1217", "yclin", "yclin1", "yclin2", "yclin3", "yclin4", "yclin5", "yclin6", "yclin7", "yclin8", "yclin9", "yclin10" };
        var account = basicProfile["APP_USER_LOGIN_ID"]?.ToString() ?? string.Empty;
        var isAdmin = adminList.Contains(account);

        var userInfo = new UserInfo
        {
            Account = account,
            UserName = basicProfile["APP_USER_CHT_NAME"]?.ToString() ?? string.Empty,
            UnitCode = basicProfile["org_code"]?.ToString() ?? string.Empty,
            UnitName = basicProfile["l1_dept_name"]?.ToString() ?? string.Empty,
            DepCode = basicProfile["parent_dept_code"]?.ToString() ?? string.Empty,
            DepName = basicProfile["parent_dept_name"]?.ToString() ?? string.Empty,
            IsAdmin = isAdmin
        };

        logger.LogDebug("Successfully parsed user info: Account={Account}, UnitCode={UnitCode}, IsAdmin={IsAdmin}",
            userInfo.Account, userInfo.UnitCode, userInfo.IsAdmin);

        return Task.FromResult<UserInfo?>(userInfo);
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error validating SSO token");
        return Task.FromResult<UserInfo?>(null);
    }
}

// 使用者資訊類別
public class UserInfo
{
    public string Account { get; set; } = string.Empty;
    public string UserName { get; set; } = string.Empty;
    public string UnitCode { get; set; } = string.Empty;
    public string UnitName { get; set; } = string.Empty;
    public string DepCode { get; set; } = string.Empty;
    public string DepName { get; set; } = string.Empty;
    public bool IsAdmin { get; set; }
}
